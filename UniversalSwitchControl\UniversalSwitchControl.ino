#include "DeviceConfig.h"
#include "WiFiManager.h"
#include "WebServerManager.h"
#include "DeviceManager.h"
#include "MQTTManager.h"
#include "TouchSensorManager.h"
#include "OTAManager.h"

// Conditional includes based on device configuration
#if USE_SHIFT_REGISTERS
#include "ShiftRegisterManager.h"
#endif

// ESP32-specific includes
#ifdef IS_ESP32
#ifdef COMPILE_FULL_COLOR_RGB
#include "FullColorRGBManager.h"
#endif
#ifdef COMPILE_COLOR_CYCLE
#include "ColorCycleManager.h"
#endif
#ifdef DEVICE_MODEL_COOLER_CONTROL
#include "CoolerControlManager.h"
#endif
#ifdef COMPILE_TEMPERATURE_SENSOR
#include "SHT30TemperatureSensor.h"
#endif
#endif

// Global variables
WiFiManager wifiManager;
DeviceManager deviceManager;                   // Use configuration-based initialization
TouchSensorManager touchManager(SWITCH_COUNT); // Initialize with configured switch count
OTAManager otaManager;                         // Use configuration-based initialization
WebServerManager webServerManager(&wifiManager, &deviceManager, &touchManager, &otaManager);
MQTTManager mqttManager(&deviceManager);

// ESP32-specific managers
#ifdef IS_ESP32
#ifdef COMPILE_FULL_COLOR_RGB
FullColorRGBManager fullColorRGBManager(SWITCH_COUNT);
#endif
#ifdef COMPILE_COLOR_CYCLE
ColorCycleManager colorCycleManager(&fullColorRGBManager, SWITCH_COUNT);
#endif
#ifdef DEVICE_MODEL_COOLER_CONTROL
CoolerControlManager coolerControlManager;
#endif
#ifdef COMPILE_TEMPERATURE_SENSOR
SHT30TemperatureSensor temperatureSensor;
#endif
#endif
unsigned long lastConnectionCheck = 0;
unsigned long lastMqttCheck = 0;
unsigned long lastMqttReconnectAttempt = 0;
unsigned long lastUpdateCheck = 0;

// MQTT callback function
void mqttCallback(char *topic, byte *payload, unsigned int length)
{
    mqttManager.handleMessage(topic, payload, length);
}

// Touch sensor callback function
void touchCallback(uint8_t switchIndex)
{
    Serial.println("=== TOUCH CALLBACK TRIGGERED ===");
    Serial.print("Switch index: ");
    Serial.println(switchIndex);

    deviceManager.toggleSwitch(switchIndex);
    Serial.print("Touch triggered switch toggle for switch ");
    Serial.println(switchIndex + 1);

    Serial.println("=== TOUCH CALLBACK COMPLETED ===");
}

// State change callback function for webserver notifications
void stateChangeCallback()
{
    Serial.println("State change detected - notifying webserver");
    webServerManager.notifyStateChange();
    // Brief yield to allow webserver to process the notification
    yield();
}

// Handle visual feedback during reset sequence
void handleResetVisualFeedback()
{
    static bool lastResetState = false;
    static unsigned long lastFlashTime = 0;
    static bool flashState = false;

    bool currentResetState = touchManager.isResetInProgress();

    // If reset state changed
    if (currentResetState != lastResetState)
    {
        lastResetState = currentResetState;
        if (currentResetState)
        {
            Serial.println("Starting reset visual feedback - flashing middle RGB red");
        }
        else
        {
            Serial.println("Stopping reset visual feedback - restoring normal colors");
            // Restore normal RGB states
            for (int i = 0; i < deviceManager.getSwitchCount(); i++)
            {
                deviceManager.updateRGBForSwitch(i);
            }
        }
    }

    // Flash red during reset sequence - only middle RGB (sensor 2, index 1)
    if (currentResetState)
    {
        unsigned long currentTime = millis();
        if (currentTime - lastFlashTime >= 250) // Flash every 250ms for urgency
        {
            lastFlashTime = currentTime;
            flashState = !flashState;

            // Flash the feedback RGB light red (configured reset feedback switch)
#if USE_SHIFT_REGISTERS
            auto *shiftRegister = deviceManager.getShiftRegister();
            if (flashState)
            {
                shiftRegister->setRGB(RESET_FEEDBACK_SWITCH, 255, 0, 0); // Feedback RGB red
            }
            else
            {
                shiftRegister->setRGB(RESET_FEEDBACK_SWITCH, 0, 0, 0); // Feedback RGB off
            }
#else
            // Direct pin control for reset feedback
            auto *directPinManager = deviceManager.getDirectPinManager();
            if (directPinManager)
            {
                if (flashState)
                {
                    directPinManager->setRGB(RESET_FEEDBACK_SWITCH, 255, 0, 0); // Red flash
                }
                else
                {
                    directPinManager->setRGB(RESET_FEEDBACK_SWITCH, 0, 0, 0); // Off
                }
            }
#endif

            // Keep other RGB lights in their normal state during reset
            for (int i = 0; i < SWITCH_COUNT; i++)
            {
                if (i != RESET_FEEDBACK_SWITCH)
                {
                    deviceManager.updateRGBForSwitch(i);
                }
            }
        }
    }
}

void setup()
{
    // Initialize serial communication
    Serial.begin(115200);
    delay(1000);
    Serial.print("\n\n");
    Serial.print(DEVICE_TYPE);
    Serial.println(" Switch Setup");

    // Initialize EEPROM for all managers
    EEPROM_INIT(EEPROM_SIZE);

    // Initialize device manager
    deviceManager.begin();

    // Set up state change callback for webserver notifications
    deviceManager.setStateChangeCallback(stateChangeCallback);
    Serial.println("Device manager state change callback set");

    // Set device manager for OTA RGB flashing
    otaManager.setDeviceManager(&deviceManager);

    // Initialize ESP32-specific managers
#ifdef IS_ESP32
#ifdef COMPILE_FULL_COLOR_RGB
    Serial.println("Initializing Full-Color RGB Manager...");
    fullColorRGBManager.begin();
    Serial.println("Full-Color RGB Manager initialized");
#endif

#ifdef COMPILE_COLOR_CYCLE
    Serial.println("Initializing Color Cycle Manager...");
    colorCycleManager.begin();
    Serial.println("Color Cycle Manager initialized");
#endif

#ifdef DEVICE_MODEL_COOLER_CONTROL
    Serial.println("Initializing Cooler Control Manager...");
    coolerControlManager.begin();
    Serial.println("Cooler Control Manager initialized");
#endif

#ifdef COMPILE_TEMPERATURE_SENSOR
    Serial.println("Initializing SHT30 Temperature Sensor...");
    if (temperatureSensor.begin())
    {
        Serial.println("SHT30 Temperature Sensor initialized successfully");
    }
    else
    {
        Serial.println("WARNING: SHT30 Temperature Sensor initialization failed");
    }
#endif
#endif

    // Initialize touch sensor manager
    Serial.println("Initializing touch sensor manager...");
    touchManager.begin();
    touchManager.setTouchCallback(touchCallback);
    Serial.println("Touch sensor manager initialized and callback set");

    // Test touch sensors immediately after initialization
    Serial.println("Testing touch sensors after initialization:");
    touchManager.testTouchSensors();

    // Set all RGB LEDs to red during boot to indicate device is loading
    Serial.println("Setting RGB LEDs to red - device loading...");
#if USE_SHIFT_REGISTERS
    auto *shiftRegister = deviceManager.getShiftRegister();
    if (shiftRegister)
    {
        for (int i = 0; i < SWITCH_COUNT; i++)
        {
            shiftRegister->setRGB(i, 255, 0, 0); // Red = loading
        }
    }
#else
    // Direct pin control for RGB loading indication
    auto *directPinManager = deviceManager.getDirectPinManager();
    if (directPinManager)
    {
        for (int i = 0; i < SWITCH_COUNT; i++)
        {
            directPinManager->setRGB(i, 255, 0, 0); // Red = loading
        }
    }
#endif

    // Always start the access point first using stored configuration
    WiFi.mode(WIFI_MODE_AP_STA);

    // Try WiFi connection first if credentials exist
    bool wifiConnected = false;
    if (wifiManager.hasStoredCredentials())
    {
        Serial.println("=== Attempting WiFi Connection ===");
        Serial.println("Found stored WiFi credentials, attempting connection...");
        Serial.println("Touch sensors will be active after boot completion");

        // Try to connect with stored credentials
        wifiConnected = wifiManager.connectWithStoredCredentials();

        if (wifiConnected)
        {
            Serial.println("=== WiFi Connection Successful! ===");
            Serial.print("Connected to: ");
            Serial.println(WiFi.SSID());
            Serial.print("IP Address: ");
            Serial.println(WiFi.localIP());
            Serial.print("Signal Strength: ");
            Serial.print(WiFi.RSSI());
            Serial.println(" dBm");

            // Initialize MQTT manager
            mqttManager.begin();
            mqttManager.setCallback(mqttCallback);

            // Initialize OTA manager
            otaManager.begin();
            Serial.println("OTA manager initialized");

            Serial.println("Connecting to MQTT...");
            mqttManager.connect();

            Serial.println("=== Starting OTA Update Check ===");
            Serial.println("WiFi is connected, checking for firmware updates...");
            otaManager.checkForUpdates();
            Serial.println("=== OTA Update Check Complete ===");
        }
        else
        {
            Serial.println("=== WiFi Connection Failed ===");
            Serial.println("Will continue with AP mode only");
        }
    }
    else
    {
        Serial.println("No stored WiFi credentials found");
        Serial.println("Device will run in AP mode only");
    }

    // Initialize AP with stored configuration from EEPROM
    if (!wifiManager.initializeAP())
    {
        Serial.println("Failed to initialize AP");
    }

    // Connect ESP32 managers to web server
#ifdef IS_ESP32
#ifdef COMPILE_FULL_COLOR_RGB
    webServerManager.setFullColorRGBManager(&fullColorRGBManager);
#endif
#ifdef COMPILE_COLOR_CYCLE
    webServerManager.setColorCycleManager(&colorCycleManager);
#endif
#ifdef COMPILE_TEMPERATURE_SENSOR
    webServerManager.setTemperatureSensor(&temperatureSensor);
#endif
#endif

    // Always start web server after WiFi connection attempt
    webServerManager.begin();

    // Initialize OTA manager if not already done
    if (!wifiConnected)
    {
        otaManager.begin();
        Serial.println("OTA manager initialized");
    }

    // Boot completion flash sequence
    Serial.println("=== Boot Complete - Flashing Status ===");
    bootCompletionFlash(wifiConnected);

    // Apply stored relay states after boot completion (prevents flickering during initialization)
    deviceManager.applyStoredRelayStates();

    Serial.println("=== Device Ready ===");
}

void loop()
{
    // Handle web server clients with memory check
    webServerManager.handleClient();

    unsigned long currentMillis = millis();

    // Handle touch sensors
    touchManager.handleTouchSensors();

    // Update ESP32-specific managers
#ifdef IS_ESP32
#ifdef COMPILE_FULL_COLOR_RGB
    fullColorRGBManager.update();
#endif
#ifdef COMPILE_COLOR_CYCLE
    colorCycleManager.update();
#endif
#ifdef DEVICE_MODEL_COOLER_CONTROL
    coolerControlManager.update();
#endif
#endif

    // Handle reset visual feedback
    handleResetVisualFeedback();

    // Handle OTA RGB flashing
    otaManager.handleRGBFlashing();

    // Feed watchdog timer regularly (platform-specific)
#ifndef IS_ESP32
    ESP.wdtFeed(); // ESP8266 only - ESP32 handles watchdog automatically
#endif

    // Memory monitoring every 30 seconds
    static unsigned long lastMemoryCheck = 0;
    if (currentMillis - lastMemoryCheck >= 30000)
    {
        lastMemoryCheck = currentMillis;
        uint32_t freeHeap = GET_FREE_HEAP();
        Serial.print("Free heap: ");
        Serial.print(freeHeap);
        Serial.println(" bytes");

        if (freeHeap < 8192)
        {
            Serial.println("WARNING: Low memory detected!");
        }
        yield();
    }

    // Check WiFi connection status every 30 seconds and try to reconnect if needed
    if (currentMillis - lastConnectionCheck >= (CONNECTION_CHECK_INTERVAL * 6))
    {
        lastConnectionCheck = currentMillis;
        yield(); // Allow other tasks to run before WiFi operations

        // Try to reconnect if we have credentials but no connection
        if (wifiManager.hasStoredCredentials() && !wifiManager.isConnected())
        {
            Serial.println("=== WiFi Reconnection Attempt ===");
            Serial.print("AP clients connected: ");
#ifdef IS_ESP32
            Serial.println(WiFi.softAPgetStationNum());
#else
            Serial.println(WiFi.softAPgetStationNum());
#endif

            // Try to reconnect using stored credentials
            bool reconnected = wifiManager.connectWithStoredCredentials();

            if (reconnected)
            {
                Serial.println("=== WiFi Reconnection Successful! ===");
                Serial.print("Connected to: ");
                Serial.println(WiFi.SSID());
                Serial.print("IP Address: ");
                Serial.println(WiFi.localIP());

                // Try to connect MQTT
                if (!mqttManager.isConnected())
                {
                    Serial.println("Connecting to MQTT...");
                    mqttManager.connect();
                }

                // Check for updates
                Serial.println("Checking for firmware updates...");
                otaManager.checkForUpdates();
            }
            else
            {
                Serial.println("WiFi reconnection failed, continuing with AP mode");
            }
        }
        else if (WiFi.status() == WL_CONNECTED)
        {
            // If we're connected but MQTT isn't, try to connect MQTT (with 5-minute delay)
            if (!mqttManager.isConnected() &&
                (currentMillis - lastMqttReconnectAttempt >= MQTT_RECONNECT_INTERVAL))
            {
                Serial.println("WiFi connected but MQTT disconnected, attempting MQTT reconnection...");
                lastMqttReconnectAttempt = currentMillis;
                mqttManager.connect();
            }
        }

        // Ensure AP+STA mode is maintained
        if (WiFi.getMode() != WIFI_MODE_AP_STA)
        {
            Serial.println("WARNING: WiFi mode changed, restoring AP+STA mode");
            WiFi.mode(WIFI_MODE_AP_STA);

            // Restore AP with stored configuration
            if (!wifiManager.initializeAP())
            {
                Serial.println("Failed to restore AP");
            }
        }

        // Debug status periodically
        static unsigned long lastStatusDebug = 0;
        if (currentMillis - lastStatusDebug >= 60000) // Every minute
        {
            lastStatusDebug = currentMillis;
            Serial.print("Status - AP Clients: ");
#ifdef IS_ESP32
            Serial.print(WiFi.softAPgetStationNum());
#else
            Serial.print(WiFi.softAPgetStationNum());
#endif
            Serial.print(", WiFi: ");
            Serial.print(WiFi.status() == WL_CONNECTED ? "CONNECTED" : "DISCONNECTED");
            Serial.print(", WebServer: ");
            Serial.println(webServerManager.isRunning() ? "RUNNING" : "STOPPED");
        }
    }

    // Handle MQTT every 2 seconds (reduced for better responsiveness)
    if (currentMillis - lastMqttCheck >= MQTT_CHECK_INTERVAL)
    {
        lastMqttCheck = currentMillis;

        if (wifiManager.isConnected())
        {
            mqttManager.loop();
            yield(); // Allow other tasks to run after MQTT operations
        }
    }

    // Check for OTA updates every 12 hours (only if WiFi is connected)
    if (wifiManager.isConnected() && currentMillis - lastUpdateCheck >= UPDATE_CHECK_INTERVAL)
    {
        lastUpdateCheck = currentMillis;
        Serial.println("=== Scheduled OTA Update Check ===");
        Serial.print("WiFi Status: Connected to ");
        Serial.println(WiFi.SSID());
        Serial.print("Signal Strength: ");
        Serial.print(WiFi.RSSI());
        Serial.println(" dBm");
        Serial.println("Performing scheduled OTA update check...");
        otaManager.checkForUpdates();
        Serial.println("=== Scheduled OTA Check Complete ===");
        yield(); // Allow other tasks to run after OTA operations
    }

    // Small delay to prevent overwhelming the ESP
    delay(1); // Reduced delay for better responsiveness
    yield();  // Ensure other tasks can run
}

// Boot completion flash sequence
void bootCompletionFlash(bool wifiConnected)
{
#if USE_SHIFT_REGISTERS
    auto *shiftRegister = deviceManager.getShiftRegister();
    if (!shiftRegister)
    {
        Serial.println("ERROR: Cannot access shift register for boot flash");
        return;
    }
#else
    auto *directPinManager = deviceManager.getDirectPinManager();
    if (!directPinManager)
    {
        Serial.println("ERROR: Cannot access direct pin manager for boot flash");
        return;
    }
#endif

    // Turn off all RGB lights first
    for (int i = 0; i < SWITCH_COUNT; i++)
    {
#if USE_SHIFT_REGISTERS
        shiftRegister->setRGB(i, 0, 0, 0);
#else
        directPinManager->setRGB(i, 0, 0, 0);
#endif
    }
    delay(200);

    if (wifiConnected)
    {
        Serial.println("Boot flash: WiFi connected (blue-green-blue)");

        // Blue flash
        for (int i = 0; i < SWITCH_COUNT; i++)
        {
#if USE_SHIFT_REGISTERS
            shiftRegister->setRGB(i, 0, 0, 255);
#else
            directPinManager->setRGB(i, 0, 0, 255);
#endif
        }
        delay(300);

        // Turn off
        for (int i = 0; i < SWITCH_COUNT; i++)
        {
#if USE_SHIFT_REGISTERS
            shiftRegister->setRGB(i, 0, 0, 0);
#else
            directPinManager->setRGB(i, 0, 0, 0);
#endif
        }
        delay(200);

        // Green flash
        for (int i = 0; i < SWITCH_COUNT; i++)
        {
#if USE_SHIFT_REGISTERS
            shiftRegister->setRGB(i, 0, 255, 0);
#else
            directPinManager->setRGB(i, 0, 255, 0);
#endif
        }
        delay(300);

        // Turn off
        for (int i = 0; i < SWITCH_COUNT; i++)
        {
#if USE_SHIFT_REGISTERS
            shiftRegister->setRGB(i, 0, 0, 0);
#else
            directPinManager->setRGB(i, 0, 0, 0);
#endif
        }
        delay(200);

        // Blue flash
        for (int i = 0; i < SWITCH_COUNT; i++)
        {
#if USE_SHIFT_REGISTERS
            shiftRegister->setRGB(i, 0, 0, 255);
#else
            directPinManager->setRGB(i, 0, 0, 255);
#endif
        }
        delay(300);
    }
    else
    {
        Serial.println("Boot flash: WiFi not connected (blue-red-blue)");

        // Blue flash
        for (int i = 0; i < SWITCH_COUNT; i++)
        {
#if USE_SHIFT_REGISTERS
            shiftRegister->setRGB(i, 0, 0, 255);
#else
            directPinManager->setRGB(i, 0, 0, 255);
#endif
        }
        delay(300);

        // Turn off
        for (int i = 0; i < SWITCH_COUNT; i++)
        {
#if USE_SHIFT_REGISTERS
            shiftRegister->setRGB(i, 0, 0, 0);
#else
            directPinManager->setRGB(i, 0, 0, 0);
#endif
        }
        delay(200);

        // Red flash
        for (int i = 0; i < SWITCH_COUNT; i++)
        {
#if USE_SHIFT_REGISTERS
            shiftRegister->setRGB(i, 255, 0, 0);
#else
            directPinManager->setRGB(i, 255, 0, 0);
#endif
        }
        delay(300);

        // Turn off
        for (int i = 0; i < SWITCH_COUNT; i++)
        {
#if USE_SHIFT_REGISTERS
            shiftRegister->setRGB(i, 0, 0, 0);
#else
            directPinManager->setRGB(i, 0, 0, 0);
#endif
        }
        delay(200);

        // Blue flash
        for (int i = 0; i < SWITCH_COUNT; i++)
        {
#if USE_SHIFT_REGISTERS
            shiftRegister->setRGB(i, 0, 0, 255);
#else
            directPinManager->setRGB(i, 0, 0, 255);
#endif
        }
        delay(300);
    }

    // Turn off all RGB lights and restore normal state
    for (int i = 0; i < SWITCH_COUNT; i++)
    {
#if USE_SHIFT_REGISTERS
        shiftRegister->setRGB(i, 0, 0, 0);
#else
        directPinManager->setRGB(i, 0, 0, 0);
#endif
        deviceManager.updateRGBForSwitch(i); // Restore normal RGB state based on switch state
    }

    Serial.println("Boot flash sequence complete");
}
